<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$action = sanitizeInput($_GET['action'] ?? 'list', 'string');
$id = sanitizeInput($_GET['id'] ?? 0, 'int');
$message = '';
$error = '';

// File upload directory
define('UPLOAD_DIR', __DIR__ . '/../uploads/');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        logSecurityEvent('CSRF Attack Attempt', 'Invalid CSRF token in pages management');
        $error = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Check rate limiting for form submissions
        if (isRateLimited('page_form_submit', 10, 300)) { // 10 submissions per 5 minutes
            logSecurityEvent('Rate Limited Form Submission', 'Too many page form submissions');
            $error = 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى بعد 5 دقائق.';
        } else {
            $category_id = sanitizeInput($_POST['category_id'] ?? 0, 'int');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $sort_order = sanitizeInput($_POST['sort_order'] ?? 0, 'int');

            $image_ar = sanitizeInput($_POST['current_image_ar'] ?? '', 'string');
            $image_en = sanitizeInput($_POST['current_image_en'] ?? '', 'string');

            // Handle file uploads with security validation
            try {
                if (isset($_FILES['image_ar']) && $_FILES['image_ar']['error'] === UPLOAD_ERR_OK) {
                    $uploadErrors = validateImageUpload($_FILES['image_ar']);
                    if (!empty($uploadErrors)) {
                        throw new Exception(implode(', ', $uploadErrors));
                    }
                    $image_ar = handle_upload($_FILES['image_ar']);
                }
                if (isset($_FILES['image_en']) && $_FILES['image_en']['error'] === UPLOAD_ERR_OK) {
                    $uploadErrors = validateImageUpload($_FILES['image_en']);
                    if (!empty($uploadErrors)) {
                        throw new Exception(implode(', ', $uploadErrors));
                    }
                    $image_en = handle_upload($_FILES['image_en']);
                }
            } catch (Exception $e) {
                logSecurityEvent('File Upload Error', $e->getMessage());
                $error = $e->getMessage();
            }
        }
    }

    if (!$error) {
        if (isset($_POST['add'])) {
            $stmt = $pdo->prepare("INSERT INTO pages (category_id, image_ar, image_en, is_active, sort_order) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$category_id, $image_ar, $image_en, $is_active, $sort_order]);
            $message = 'تم إضافة الصفحة بنجاح!';
        } elseif (isset($_POST['edit'])) {
            $stmt = $pdo->prepare("UPDATE pages SET category_id = ?, image_ar = ?, image_en = ?, is_active = ?, sort_order = ? WHERE id = ?");
            $stmt->execute([$category_id, $image_ar, $image_en, $is_active, $sort_order, $id]);
            $message = 'تم تحديث الصفحة بنجاح!';
        }
        $action = 'list'; // Go back to list view
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    // Optional: Delete image files from server
    $stmt = $pdo->prepare("SELECT image_ar, image_en FROM pages WHERE id = ?");
    $stmt->execute([$id]);
    $images = $stmt->fetch();
    if ($images) {
        if ($images['image_ar'] && file_exists(UPLOAD_DIR . $images['image_ar'])) {
            unlink(UPLOAD_DIR . $images['image_ar']);
        }
        if ($images['image_en'] && file_exists(UPLOAD_DIR . $images['image_en'])) {
            unlink(UPLOAD_DIR . $images['image_en']);
        }
    }

    $stmt = $pdo->prepare("DELETE FROM pages WHERE id = ?");
    $stmt->execute([$id]);
    $message = 'تم حذف الصفحة.';
    $action = 'list';
}

function handle_upload($file) {
    // Generate secure filename
    $secure_filename = generateSecureFilename($file['name']);
    $target_file = UPLOAD_DIR . $secure_filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $target_file)) {
        // Set proper file permissions
        chmod($target_file, 0644);

        logSecurityEvent('File Upload Success', "File: $secure_filename");
        return $secure_filename;
    } else {
        throw new Exception("حدث خطأ أثناء رفع الملف.");
    }
}

?>

<?php if ($message): ?><div class="alert alert-success"><?php echo $message; ?></div><?php endif; ?>
<?php if ($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>

<?php if ($action === 'list'): ?>
    <a href="admin.php?page=pages&action=add" class="btn btn-primary" style="margin-bottom: 20px;">إضافة صفحة جديدة</a>
    <table>
        <thead>
            <tr>
                <th>صورة مصغرة</th>
                <th>القسم</th>
                <th>الترتيب</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $stmt = $pdo->query("SELECT p.*, c.name_ar as category_name FROM pages p JOIN categories c ON p.category_id = c.id ORDER BY c.sort_order ASC, p.sort_order ASC");
            while ($row = $stmt->fetch()):
            ?>
            <tr>
                <td><img src="uploads/<?php echo htmlspecialchars($row['image_ar']); ?>" alt="" width="100"></td>
                <td><?php echo htmlspecialchars($row['category_name']); ?></td>
                <td><?php echo htmlspecialchars($row['sort_order']); ?></td>
                <td><?php echo $row['is_active'] ? 'مفعل' : 'معطل'; ?></td>
                <td>
                    <a href="admin.php?page=pages&action=edit&id=<?php echo $row['id']; ?>">تعديل</a> |
                    <a href="admin.php?page=pages&action=delete&id=<?php echo $row['id']; ?>" onclick="return confirm('هل أنت متأكد من حذف هذه الصفحة؟');">حذف</a>
                </td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>

<?php elseif ($action === 'add' || $action === 'edit'): 
    $page_data = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM pages WHERE id = ?");
        $stmt->execute([$id]);
        $page_data = $stmt->fetch();
    }
    $categories = $pdo->query("SELECT id, name_ar FROM categories ORDER BY sort_order ASC")->fetchAll();
?>
    <h3><?php echo $action === 'add' ? 'إضافة صفحة جديدة' : 'تعديل الصفحة'; ?></h3>
    <form method="POST" action="admin.php?page=pages&action=<?php echo $action; ?>&id=<?php echo $id; ?>" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
        <div class="form-group">
            <label for="category_id">القسم</label>
            <select id="category_id" name="category_id" required>
                <?php foreach ($categories as $category): ?>
                <option value="<?php echo $category['id']; ?>" <?php echo (isset($page_data['category_id']) && $page_data['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($category['name_ar']); ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="form-group">
            <label for="image_ar">صورة الصفحة (بالعربية)</label>
            <input type="file" id="image_ar" name="image_ar">
            <?php if (isset($page_data['image_ar']) && $page_data['image_ar']): ?>
                <p>الحالية: <?php echo htmlspecialchars($page_data['image_ar']); ?></p>
                <img src="uploads/<?php echo htmlspecialchars($page_data['image_ar']); ?>" width="150" style="margin-top:10px;">
                <input type="hidden" name="current_image_ar" value="<?php echo htmlspecialchars($page_data['image_ar']); ?>">
            <?php endif; ?>
        </div>
        <div class="form-group">
            <label for="image_en">صورة الصفحة (بالإنجليزية) - (اختياري)</label>
            <input type="file" id="image_en" name="image_en">
            <p><small>إذا تركت فارغة، سيتم استخدام الصورة العربية.</small></p>
            <?php if (isset($page_data['image_en']) && $page_data['image_en']): ?>
                <p>الحالية: <?php echo htmlspecialchars($page_data['image_en']); ?></p>
                <img src="uploads/<?php echo htmlspecialchars($page_data['image_en']); ?>" width="150" style="margin-top:10px;">
                <input type="hidden" name="current_image_en" value="<?php echo htmlspecialchars($page_data['image_en']); ?>">
            <?php endif; ?>
        </div>
        <div class="form-group">
            <label for="sort_order">ترتيب العرض</label>
            <input type="number" id="sort_order" name="sort_order" value="<?php echo htmlspecialchars($page_data['sort_order'] ?? '0'); ?>" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" value="1" <?php echo (isset($page_data['is_active']) && $page_data['is_active']) || $action === 'add' ? 'checked' : ''; ?>>
                تفعيل الصفحة
            </label>
        </div>
        <button type="submit" name="<?php echo $action; ?>" class="btn btn-primary"><?php echo $action === 'add' ? 'إضافة' : 'تحديث'; ?></button>
        <a href="admin.php?page=pages" class="btn btn-secondary">إلغاء</a>
    </form>
<?php endif; ?>