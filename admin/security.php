<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$action = sanitizeInput($_GET['action'] ?? 'logs', 'string');
$message = '';
$error = '';

// Handle cleanup action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        logSecurityEvent('CSRF Attack Attempt', 'Invalid CSRF token in security cleanup');
        $error = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        $days = sanitizeInput($_POST['cleanup_days'] ?? 30, 'int');
        if ($days > 0 && $days <= 365) {
            $deleted = cleanOldSecurityLogs($days);
            logSecurityEvent('Security Logs Cleaned', "Deleted $deleted old entries");
            $message = "تم حذف $deleted سجل أمان قديم.";
        } else {
            $error = 'عدد الأيام يجب أن يكون بين 1 و 365.';
        }
    }
}

// Get security statistics
$stats = getSecurityStats(7);

?>

<style>
.security-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #8B4513;
}

.stat-card h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #8B4513;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.logs-table th,
.logs-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

.logs-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.severity-INFO { color: #28a745; }
.severity-WARNING { color: #ffc107; }
.severity-ERROR { color: #dc3545; }
.severity-CRITICAL { color: #6f42c1; }

.cleanup-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}
</style>

<?php if ($message): ?>
    <div class="alert alert-success"><?php echo $message; ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?php echo $error; ?></div>
<?php endif; ?>

<h2>🛡️ سجلات الأمان</h2>

<?php if ($stats): ?>
<div class="security-stats">
    <div class="stat-card">
        <h4>إجمالي الأحداث (7 أيام)</h4>
        <div class="stat-number"><?php echo $stats['total_events']; ?></div>
    </div>
    
    <div class="stat-card">
        <h4>أكثر الأحداث</h4>
        <?php if (!empty($stats['events_by_type'])): ?>
            <div><?php echo htmlspecialchars($stats['events_by_type'][0]['event_type']); ?></div>
            <div class="stat-number"><?php echo $stats['events_by_type'][0]['count']; ?></div>
        <?php else: ?>
            <div class="stat-number">0</div>
        <?php endif; ?>
    </div>
    
    <div class="stat-card">
        <h4>أكثر IP نشاطاً</h4>
        <?php if (!empty($stats['top_ips'])): ?>
            <div><?php echo htmlspecialchars($stats['top_ips'][0]['ip_address']); ?></div>
            <div class="stat-number"><?php echo $stats['top_ips'][0]['count']; ?></div>
        <?php else: ?>
            <div class="stat-number">-</div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<div class="cleanup-form">
    <h3>تنظيف السجلات القديمة</h3>
    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
        <label for="cleanup_days">حذف السجلات الأقدم من (بالأيام):</label>
        <input type="number" id="cleanup_days" name="cleanup_days" value="30" min="1" max="365" required>
        <button type="submit" name="cleanup" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من حذف السجلات القديمة؟');">تنظيف السجلات</button>
    </form>
</div>

<?php
// Display recent security logs
try {
    // Check if security_logs table exists, create if not
    $result = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='security_logs'");

    if ($result->rowCount() == 0) {
        // Create security logs table
        $sql = "
        CREATE TABLE `security_logs` (
            `id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
            `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
            `ip_address` TEXT,
            `user_agent` TEXT,
            `event_type` TEXT NOT NULL,
            `details` TEXT,
            `severity` TEXT DEFAULT 'INFO'
        );

        CREATE INDEX idx_security_logs_timestamp ON security_logs(timestamp);
        CREATE INDEX idx_security_logs_event_type ON security_logs(event_type);
        CREATE INDEX idx_security_logs_ip ON security_logs(ip_address);
        ";

        $pdo->exec($sql);

        // Insert initial log entry
        $stmt = $pdo->prepare("INSERT INTO security_logs (event_type, details, severity, ip_address) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Table Creation', 'Security logs table created automatically', 'INFO', $_SERVER['REMOTE_ADDR'] ?? 'system']);

        $message = 'تم إنشاء جدول سجلات الأمان تلقائياً.';
    }

    $stmt = $pdo->query("SELECT * FROM security_logs ORDER BY timestamp DESC LIMIT 50");
    $logs = $stmt->fetchAll();
    
    if (!empty($logs)):
?>
<h3>آخر 50 حدث أمان</h3>
<table class="logs-table">
    <thead>
        <tr>
            <th>الوقت</th>
            <th>نوع الحدث</th>
            <th>التفاصيل</th>
            <th>عنوان IP</th>
            <th>الخطورة</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($logs as $log): ?>
        <tr>
            <td><?php echo htmlspecialchars($log['timestamp']); ?></td>
            <td><?php echo htmlspecialchars($log['event_type']); ?></td>
            <td><?php echo htmlspecialchars($log['details']); ?></td>
            <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
            <td class="severity-<?php echo htmlspecialchars($log['severity']); ?>">
                <?php echo htmlspecialchars($log['severity']); ?>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<?php else: ?>
<p>لا توجد سجلات أمان متاحة.</p>
<?php endif; ?>

<?php
} catch (Exception $e) {
    echo '<p>خطأ في قراءة سجلات الأمان: ' . htmlspecialchars($e->getMessage()) . '</p>';
}
?>
