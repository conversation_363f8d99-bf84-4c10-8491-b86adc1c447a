<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$action = sanitizeInput($_GET['action'] ?? 'list', 'string');
$id = sanitizeInput($_GET['id'] ?? 0, 'int');
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        logSecurityEvent('CSRF Attack Attempt', 'Invalid CSRF token in categories management');
        $error = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Check rate limiting
        if (isRateLimited('category_form_submit', 10, 300)) {
            logSecurityEvent('Rate Limited Form Submission', 'Too many category form submissions');
            $error = 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى بعد 5 دقائق.';
        } else {
            $name_ar = sanitizeInput($_POST['name_ar'] ?? '', 'html');
            $name_en = sanitizeInput($_POST['name_en'] ?? '', 'html');
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $sort_order = sanitizeInput($_POST['sort_order'] ?? 0, 'int');

            if (isset($_POST['add'])) {
                $stmt = $pdo->prepare("INSERT INTO categories (name_ar, name_en, is_active, sort_order) VALUES (?, ?, ?, ?)");
                $stmt->execute([$name_ar, $name_en, $is_active, $sort_order]);
                logSecurityEvent('Category Added', "Name: $name_ar");
                $message = 'تم إضافة القسم بنجاح!';
            } elseif (isset($_POST['edit'])) {
                $stmt = $pdo->prepare("UPDATE categories SET name_ar = ?, name_en = ?, is_active = ?, sort_order = ? WHERE id = ?");
                $stmt->execute([$name_ar, $name_en, $is_active, $sort_order, $id]);
                logSecurityEvent('Category Updated', "ID: $id, Name: $name_ar");
                $message = 'تم تحديث القسم بنجاح!';
            }
            $action = 'list'; // Go back to list view
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    // Check rate limiting for delete operations
    if (isRateLimited('category_delete', 5, 300)) {
        logSecurityEvent('Rate Limited Delete Attempt', 'Too many category delete attempts');
        $error = 'تم تجاوز عدد محاولات الحذف المسموح. حاول مرة أخرى بعد 5 دقائق.';
    } else {
        // Also delete associated pages
        $stmt = $pdo->prepare("DELETE FROM pages WHERE category_id = ?");
        $stmt->execute([$id]);

        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->execute([$id]);

        logSecurityEvent('Category Deleted', "ID: $id");
        $message = 'تم حذف القسم والصفحات المرتبطة به.';
        $action = 'list';
    }
}

?>

<?php if ($message): ?>
    <div class="alert alert-success"><?php echo $message; ?></div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <a href="admin.php?page=categories&action=add" class="btn btn-primary" style="margin-bottom: 20px;">إضافة قسم جديد</a>
    <table>
        <thead>
            <tr>
                <th>الاسم (عربي)</th>
                <th>الاسم (إنجليزي)</th>
                <th>الترتيب</th>
                <th>الحالة</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $stmt = $pdo->query("SELECT * FROM categories ORDER BY sort_order ASC");
            while ($row = $stmt->fetch()):
            ?>
            <tr>
                <td><?php echo htmlspecialchars($row['name_ar']); ?></td>
                <td><?php echo htmlspecialchars($row['name_en']); ?></td>
                <td><?php echo htmlspecialchars($row['sort_order']); ?></td>
                <td><?php echo $row['is_active'] ? 'مفعل' : 'معطل'; ?></td>
                <td>
                    <a href="admin.php?page=categories&action=edit&id=<?php echo $row['id']; ?>">تعديل</a> |
                    <a href="admin.php?page=categories&action=delete&id=<?php echo $row['id']; ?>" onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟ سيتم حذف جميع الصفحات المرتبطة به.');">حذف</a>
                </td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>

<?php elseif ($action === 'add' || $action === 'edit'): 
    $category = null;
    if ($action === 'edit' && $id) {
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $category = $stmt->fetch();
    }
?>
    <h3><?php echo $action === 'add' ? 'إضافة قسم جديد' : 'تعديل القسم'; ?></h3>
    <form method="POST" action="admin.php?page=categories&action=<?php echo $action; ?>&id=<?php echo $id; ?>">
        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
        <div class="form-group">
            <label for="name_ar">الاسم (بالعربية)</label>
            <input type="text" id="name_ar" name="name_ar" value="<?php echo htmlspecialchars($category['name_ar'] ?? ''); ?>" required>
        </div>
        <div class="form-group">
            <label for="name_en">الاسم (بالإنجليزية)</label>
            <input type="text" id="name_en" name="name_en" value="<?php echo htmlspecialchars($category['name_en'] ?? ''); ?>">
        </div>
        <div class="form-group">
            <label for="sort_order">ترتيب العرض</label>
            <input type="number" id="sort_order" name="sort_order" value="<?php echo htmlspecialchars($category['sort_order'] ?? '0'); ?>" required>
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" value="1" <?php echo (isset($category['is_active']) && $category['is_active']) || $action === 'add' ? 'checked' : ''; ?>>
                تفعيل القسم
            </label>
        </div>
        <button type="submit" name="<?php echo $action; ?>" class="btn btn-primary"><?php echo $action === 'add' ? 'إضافة' : 'تحديث'; ?></button>
        <a href="admin.php?page=categories" class="btn btn-secondary">إلغاء</a>
    </form>
<?php endif; ?>