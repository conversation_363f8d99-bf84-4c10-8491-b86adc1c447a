<?php
// Ensure the script is not accessed directly
if (!defined('DB_FILE')) {
    exit('Direct script access is not allowed.');
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        logSecurityEvent('CSRF Attack Attempt', 'Invalid CSRF token in settings');
        $error = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Check rate limiting
        if (isRateLimited('settings_update', 5, 300)) {
            logSecurityEvent('Rate Limited Settings Update', 'Too many settings update attempts');
            $error = 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى بعد 5 دقائق.';
        } else {
            // Update general settings with sanitization
            $settings_to_update = [
                'site_title' => sanitizeInput($_POST['site_title'] ?? '', 'html'),
                'primary_color' => sanitizeInput($_POST['primary_color'] ?? '#8B4513', 'string'),
                'secondary_color' => sanitizeInput($_POST['secondary_color'] ?? '#FFFFFF', 'string'),
                'button_bg_color' => sanitizeInput($_POST['button_bg_color'] ?? '#FFFFFF', 'string'),
            ];

            // Validate color values
            foreach (['primary_color', 'secondary_color', 'button_bg_color'] as $color_key) {
                if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $settings_to_update[$color_key])) {
                    $error = 'قيمة اللون غير صحيحة: ' . $color_key;
                    break;
                }
            }

            if (!$error) {
                // Update active languages
                $active_langs = isset($_POST['active_langs']) && is_array($_POST['active_langs']) ?
                    implode(',', array_map(function($lang) { return sanitizeInput($lang, 'string'); }, $_POST['active_langs'])) : '';
                $settings_to_update['lang_active'] = $active_langs;

                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
                foreach ($settings_to_update as $key => $value) {
                    $stmt->execute([$value, $key]);
                }

                // Update admin password if provided
                if (!empty($_POST['admin_pass'])) {
                    if ($_POST['admin_pass'] === $_POST['admin_pass_confirm']) {
                        // Validate password strength
                        if (strlen($_POST['admin_pass']) < 8) {
                            $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل.';
                        } else {
                            $hashed_password = password_hash($_POST['admin_pass'], PASSWORD_DEFAULT);
                            $stmt->execute([$hashed_password, 'admin_pass']);
                            logSecurityEvent('Password Changed', 'Admin password updated');
                        }
                    } else {
                        $error = 'كلمتا المرور غير متطابقتين.';
                    }
                }

                if (!$error) {
                    logSecurityEvent('Settings Updated', 'System settings modified');
                    $message = 'تم تحديث الإعدادات بنجاح!';
                }
            }
        }
    }
    
    // Refresh settings variable
    $stmt = $pdo->query("SELECT * FROM settings");
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

$current_active_langs = explode(',', $settings['lang_active']);
?>

<?php if ($message): ?><div class="alert alert-success" style="margin-bottom: 20px;"><?php echo $message; ?></div><?php endif; ?>
<?php if ($error): ?><div class="alert alert-danger" style="margin-bottom: 20px;"><?php echo $error; ?></div><?php endif; ?>

<form method="POST" action="admin.php?page=settings">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
    <h3>الإعدادات العامة</h3>
    <div class="form-group">
        <label for="site_title">عنوان الموقع</label>
        <input type="text" id="site_title" name="site_title" value="<?php echo htmlspecialchars($settings['site_title']); ?>" required>
    </div>
    <div class="form-group">
        <label for="primary_color">اللون الأساسي (للعناصر النشطة)</label>
        <input type="color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($settings['primary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="secondary_color">لون الشريط السفلي</label>
        <input type="color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($settings['secondary_color']); ?>">
    </div>
    <div class="form-group">
        <label for="button_bg_color">لون خلفية أزرار الأقسام</label>
        <input type="color" id="button_bg_color" name="button_bg_color" value="<?php echo htmlspecialchars($settings['button_bg_color'] ?? '#FFFFFF'); ?>">
    </div>

    <hr style="margin: 30px 0;">

    <h3>إعدادات اللغة</h3>
    <div class="form-group">
        <label>اللغات المفعلة</label>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="ar" <?php echo in_array('ar', $current_active_langs) ? 'checked' : ''; ?>> العربية</label>
        </div>
        <div>
            <label><input type="checkbox" name="active_langs[]" value="en" <?php echo in_array('en', $current_active_langs) ? 'checked' : ''; ?>> الإنجليزية</label>
        </div>
    </div>

    <hr style="margin: 30px 0;">

    <h3>تغيير كلمة المرور</h3>
    <div class="form-group">
        <label for="admin_pass">كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass" name="admin_pass" placeholder="اتركها فارغة لعدم التغيير">
    </div>
    <div class="form-group">
        <label for="admin_pass_confirm">تأكيد كلمة المرور الجديدة</label>
        <input type="password" id="admin_pass_confirm" name="admin_pass_confirm">
    </div>

    <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
</form>