<?php
/**
 * System Requirements Check
 * فحص متطلبات النظام
 */

$checks = [];
$overall_status = true;

// Check PHP version
$php_version = phpversion();
$php_required = '7.4.0';
$php_ok = version_compare($php_version, $php_required, '>=');
$checks['php'] = [
    'name' => 'إصدار PHP',
    'current' => $php_version,
    'required' => $php_required . '+',
    'status' => $php_ok,
    'critical' => true
];
if (!$php_ok) $overall_status = false;

// Check SQLite support
$sqlite_ok = extension_loaded('sqlite3');
$checks['sqlite'] = [
    'name' => 'دعم SQLite',
    'current' => $sqlite_ok ? 'متوفر' : 'غير متوفر',
    'required' => 'مطلوب',
    'status' => $sqlite_ok,
    'critical' => true
];
if (!$sqlite_ok) $overall_status = false;

// Check GD extension
$gd_ok = extension_loaded('gd');
$checks['gd'] = [
    'name' => 'مكتبة GD',
    'current' => $gd_ok ? 'متوفرة' : 'غير متوفرة',
    'required' => 'مطلوبة لمعالجة الصور',
    'status' => $gd_ok,
    'critical' => true
];
if (!$gd_ok) $overall_status = false;

// Check file permissions
$writable = is_writable('.');
$checks['permissions'] = [
    'name' => 'أذونات الكتابة',
    'current' => $writable ? 'متاحة' : 'غير متاحة',
    'required' => 'مطلوبة',
    'status' => $writable,
    'critical' => true
];
if (!$writable) $overall_status = false;

// Check upload settings
$upload_max = ini_get('upload_max_filesize');
$post_max = ini_get('post_max_size');
$memory_limit = ini_get('memory_limit');

$checks['upload'] = [
    'name' => 'حد رفع الملفات',
    'current' => $upload_max,
    'required' => '8M+',
    'status' => true,
    'critical' => false
];

$checks['post'] = [
    'name' => 'حد POST',
    'current' => $post_max,
    'required' => '8M+',
    'status' => true,
    'critical' => false
];

$checks['memory'] = [
    'name' => 'حد الذاكرة',
    'current' => $memory_limit,
    'required' => '64M+',
    'status' => true,
    'critical' => false
];

// Check optional extensions
$curl_ok = extension_loaded('curl');
$checks['curl'] = [
    'name' => 'مكتبة cURL',
    'current' => $curl_ok ? 'متوفرة' : 'غير متوفرة',
    'required' => 'اختيارية (للتكاملات المستقبلية)',
    'status' => $curl_ok,
    'critical' => false
];

$mbstring_ok = extension_loaded('mbstring');
$checks['mbstring'] = [
    'name' => 'مكتبة mbstring',
    'current' => $mbstring_ok ? 'متوفرة' : 'غير متوفرة',
    'required' => 'اختيارية (لدعم أفضل للنصوص)',
    'status' => $mbstring_ok,
    'critical' => false
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص متطلبات النظام</title>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            margin: 40px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-overall {
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }
        .status-ok {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .check-ok {
            color: #28a745;
            font-weight: bold;
        }
        .check-error {
            color: #dc3545;
            font-weight: bold;
        }
        .check-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .critical {
            background-color: #fff5f5;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .btn:hover {
            background: #5D4037;
        }
        .btn-disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص متطلبات النظام</h1>
        
        <div class="status-overall <?php echo $overall_status ? 'status-ok' : 'status-error'; ?>">
            <?php if ($overall_status): ?>
                ✅ النظام جاهز للتثبيت!
            <?php else: ?>
                ❌ يجب حل المشاكل التالية قبل التثبيت
            <?php endif; ?>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>المتطلب</th>
                    <th>الحالة الحالية</th>
                    <th>المطلوب</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($checks as $key => $check): ?>
                <tr <?php echo $check['critical'] && !$check['status'] ? 'class="critical"' : ''; ?>>
                    <td><?php echo $check['name']; ?></td>
                    <td><?php echo $check['current']; ?></td>
                    <td><?php echo $check['required']; ?></td>
                    <td>
                        <?php if ($check['status']): ?>
                            <span class="check-ok">✅ متوفر</span>
                        <?php elseif ($check['critical']): ?>
                            <span class="check-error">❌ مطلوب</span>
                        <?php else: ?>
                            <span class="check-warning">⚠️ اختياري</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <?php if (!$overall_status): ?>
        <div class="note">
            <h3>⚠️ مشاكل يجب حلها:</h3>
            <ul>
                <?php foreach ($checks as $check): ?>
                    <?php if ($check['critical'] && !$check['status']): ?>
                    <li><strong><?php echo $check['name']; ?>:</strong> <?php echo $check['required']; ?></li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
            <p><strong>تواصل مع مزود الاستضافة لحل هذه المشاكل.</strong></p>
        </div>
        <?php endif; ?>
        
        <div class="actions">
            <?php if ($overall_status): ?>
                <a href="install.php" class="btn">🚀 بدء التثبيت</a>
            <?php else: ?>
                <span class="btn btn-disabled">🚀 بدء التثبيت</span>
            <?php endif; ?>
            
            <a href="system_check.php" class="btn" style="background: #17a2b8;">🔄 إعادة الفحص</a>
        </div>
        
        <div class="note">
            <h4>📝 ملاحظات:</h4>
            <ul>
                <li>تأكد من أن مجلد الموقع قابل للكتابة (أذونات 755)</li>
                <li>يُنصح بتفعيل HTTPS لحماية أفضل</li>
                <li>تأكد من وجود مساحة تخزين كافية (50MB على الأقل)</li>
                <li>يمكنك حذف هذا الملف بعد التثبيت</li>
            </ul>
        </div>
    </div>
</body>
</html>
