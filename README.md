# 📱 نظام القائمة الذكية - Smart Menu System

نظام عرض قوائم الطعام الذكي للمطاعم والمحلات التجارية مع لوحة تحكم متقدمة ونظام أمان شامل.

## ✨ المميزات الرئيسية

### 🌐 **للعملاء (الواجهة الأمامية)**
- **تصميم متجاوب** يعمل على جميع الأجهزة (هاتف، تابلت، حاسوب)
- **دعم متعدد اللغات** (عربي/إنجليزي) مع حفظ اللغة المختارة
- **تنقل سلس** بالسحب (swipe) مثل التطبيقات الحديثة
- **تحميل سريع** مع نظام تحميل مسبق للصور
- **ألوان قابلة للتخصيص** حسب هوية المحل
- **تصنيفات منظمة** للتنقل السريع بين أقسام القائمة

### 🛡️ **لوحة التحكم الآمنة**
- **نظام أمان متقدم** مع حماية شاملة
- **إدارة الأقسام** (إضافة، تعديل، حذف، ترتيب)
- **إدارة الصفحات** مع رفع الصور المحسنة
- **تخصيص الألوان** والإعدادات العامة
- **سجلات الأمان** لمراقبة النشاطات
- **واجهة عربية** سهلة الاستخدام

### 🔒 **الأمان والحماية**
- ✅ حماية من SQL Injection
- ✅ حماية من XSS Attacks
- ✅ حماية CSRF
- ✅ Rate Limiting ضد الهجمات
- ✅ تشفير الجلسات
- ✅ فحص أمان الملفات المرفوعة
- ✅ سجلات أمنية شاملة

## 📋 متطلبات النظام

- **PHP 7.4+** مع دعم SQLite و GD
- **خادم ويب** (Apache/Nginx)
- **مساحة تخزين** 50MB على الأقل
- **SSL Certificate** (مُوصى به)

## 🚀 التنصيب السريع

### الخطوة 1: رفع الملفات
```bash
# ارفع جميع الملفات إلى مجلد الموقع
# تأكد من أن المجلد قابل للكتابة (755)
```

### الخطوة 2: تشغيل المثبت
1. اذهب إلى: `http://yoursite.com/install.php`
2. اتبع التعليمات على الشاشة
3. سيتم إنشاء قاعدة البيانات تلقائياً

### الخطوة 3: الدخول للوحة التحكم
- **الرابط:** `http://yoursite.com/admin.php`
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin`

⚠️ **مهم:** غيّر كلمة المرور فوراً من الإعدادات!

## 📁 هيكل الملفات

```
├── index.php              # الصفحة الرئيسية
├── admin.php             # لوحة التحكم
├── install.php           # مثبت النظام
├── config.php            # ملف الإعدادات (يُنشأ تلقائياً)
├── .htaccess            # إعدادات الأمان
├── admin/               # ملفات لوحة التحكم
│   ├── dashboard.php    # الرئيسية
│   ├── categories.php   # إدارة الأقسام
│   ├── pages.php        # إدارة الصفحات
│   ├── settings.php     # الإعدادات
│   ├── security.php     # سجلات الأمان
│   ├── header.php       # رأس الصفحة
│   └── footer.php       # تذييل الصفحة
├── includes/            # ملفات النظام
│   ├── security.php     # وظائف الأمان
│   └── image_optimizer.php # تحسين الصور
├── assets/              # الموارد
│   ├── css/            # ملفات التنسيق
│   └── js/             # ملفات JavaScript
├── uploads/             # مجلد الصور المرفوعة
├── logs/                # سجلات النظام
└── database.db          # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎨 التخصيص

### تغيير الألوان
1. اذهب إلى **لوحة التحكم > الإعدادات**
2. اختر الألوان المناسبة لهوية محلك
3. احفظ التغييرات

### إضافة المحتوى
1. **إنشاء الأقسام:** اذهب إلى **الأقسام** وأضف أقسام القائمة
2. **رفع الصور:** اذهب إلى **الصفحات** وارفع صور القائمة
3. **ترتيب العرض:** استخدم حقل "ترتيب العرض" لتنظيم المحتوى

## 🔧 الإعدادات المتقدمة

### تحسين الأداء
- تأكد من تفعيل **mod_deflate** لضغط الملفات
- استخدم **CDN** لتسريع تحميل الصور
- فعّل **browser caching** في .htaccess

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
cp database.db backup_$(date +%Y%m%d).db

# نسخ احتياطي للصور
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في الأذونات**
```bash
chmod 755 .
chmod 644 *.php
chmod 755 uploads/
chmod 755 logs/
```

**2. مشكلة في رفع الصور**
- تحقق من إعدادات PHP: `upload_max_filesize` و `post_max_size`
- تأكد من أن مجلد uploads قابل للكتابة

**3. مشكلة في قاعدة البيانات**
- تحقق من أذونات الكتابة في مجلد الموقع
- تأكد من دعم PHP لـ SQLite

## 🔐 الأمان

### إعدادات الأمان الموصى بها
1. **غيّر كلمة مرور المدير** فوراً
2. **استخدم HTTPS** لحماية البيانات
3. **راجع سجلات الأمان** بانتظام
4. **حدّث النظام** عند توفر تحديثات

### مراقبة الأمان
- اذهب إلى **لوحة التحكم > سجلات الأمان**
- راقب محاولات الدخول المشبوهة
- نظّف السجلات القديمة دورياً

## 📞 الدعم والمساعدة

### الأسئلة الشائعة
- **كيف أضيف لغة جديدة؟** حالياً النظام يدعم العربية والإنجليزية فقط
- **هل يمكن إضافة المزيد من الأقسام؟** نعم، لا يوجد حد أقصى
- **كيف أغير شعار الموقع؟** من خلال إعدادات عنوان الموقع

### التحديثات المستقبلية
- 🔄 نظام السلة والطلبات
- 🛒 واجهة تفاعلية للمنتجات
- 📊 تقارير وإحصائيات متقدمة
- 🔗 تكامل مع WhatsApp

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

---

**تم التطوير بـ ❤️ لخدمة المجتمع العربي**

للمزيد من المساعدة أو الاستفسارات، يرجى مراجعة الوثائق أو التواصل مع فريق الدعم.
