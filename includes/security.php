<?php
/**
 * Security Functions for SAA Menu System
 * Enhanced security measures and utilities
 */

// Prevent direct access
if (!defined('DB_FILE')) {
    exit('Direct access not allowed');
}

/**
 * Enhanced session security
 */
function initSecureSession() {
    // Session security settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // Set session timeout (30 minutes)
    ini_set('session.gc_maxlifetime', 1800);
    
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
        session_unset();
        session_destroy();
        session_start();
    }
    $_SESSION['last_activity'] = time();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['created'])) {
        $_SESSION['created'] = time();
    } else if (time() - $_SESSION['created'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['created'] = time();
    }
}

/**
 * Input sanitization functions
 */
function sanitizeInput($input, $type = 'string') {
    switch ($type) {
        case 'string':
            return filter_var(trim($input), FILTER_SANITIZE_STRING);
        case 'email':
            return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT);
        case 'float':
            return filter_var($input, FILTER_VALIDATE_FLOAT);
        case 'url':
            return filter_var(trim($input), FILTER_SANITIZE_URL);
        case 'html':
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        default:
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * File upload security
 */
function validateImageUpload($file, $maxSize = 5242880) { // 5MB default
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'لم يتم رفع الملف بشكل صحيح';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        $errors[] = 'حجم الملف كبير جداً. الحد الأقصى ' . ($maxSize / 1024 / 1024) . ' ميجابايت';
    }
    
    // Check if it's actually an image
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        $errors[] = 'الملف ليس صورة صحيحة';
        return $errors;
    }
    
    // Check MIME type
    $allowedMimes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($imageInfo['mime'], $allowedMimes)) {
        $errors[] = 'نوع الصورة غير مدعوم. المسموح: JPEG, PNG, GIF';
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    if (!in_array($extension, $allowedExtensions)) {
        $errors[] = 'امتداد الملف غير مدعوم';
    }
    
    // Check for embedded scripts
    $fileContent = file_get_contents($file['tmp_name']);
    $dangerousPatterns = ['<?php', '<?=', '<script', 'javascript:', 'vbscript:'];
    foreach ($dangerousPatterns as $pattern) {
        if (stripos($fileContent, $pattern) !== false) {
            $errors[] = 'الملف يحتوي على محتوى غير آمن';
            break;
        }
    }
    
    return $errors;
}

/**
 * Generate secure filename
 */
function generateSecureFilename($originalName) {
    $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    
    // Sanitize basename
    $basename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $basename);
    $basename = substr($basename, 0, 50); // Limit length
    
    // Add timestamp and random string
    $timestamp = time();
    $random = bin2hex(random_bytes(8));
    
    return $timestamp . '_' . $random . '_' . $basename . '.' . $extension;
}

/**
 * Log security events to both file and database
 */
function logSecurityEvent($event, $details = '', $severity = 'INFO') {
    // Log to file (existing functionality)
    $logFile = __DIR__ . '/../logs/security.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $logEntry = "[{$timestamp}] IP: {$ip} | Event: {$event} | Details: {$details} | Severity: {$severity} | User-Agent: {$userAgent}" . PHP_EOL;

    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

    // Log to database if available
    try {
        if (defined('DB_FILE') && file_exists(DB_FILE)) {
            $pdo = new PDO('sqlite:' . __DIR__ . '/../' . DB_FILE);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if security_logs table exists
            $result = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='security_logs'");
            if ($result->rowCount() > 0) {
                $stmt = $pdo->prepare("INSERT INTO security_logs (ip_address, user_agent, event_type, details, severity) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$ip, $userAgent, $event, $details, $severity]);
            }
        }
    } catch (Exception $e) {
        // Silently fail database logging to avoid breaking the application
        error_log("Security logging error: " . $e->getMessage());
    }
}

/**
 * Check for suspicious activity
 */
function detectSuspiciousActivity() {
    $suspiciousPatterns = [
        'union select',
        'drop table',
        'insert into',
        'delete from',
        'update set',
        '<script',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        '../',
        '..\\',
        'etc/passwd',
        'boot.ini'
    ];
    
    $requestData = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($requestData as $key => $value) {
        if (is_string($value)) {
            foreach ($suspiciousPatterns as $pattern) {
                if (stripos($value, $pattern) !== false) {
                    logSecurityEvent('Suspicious Activity Detected', "Pattern: {$pattern} in {$key}");
                    return true;
                }
            }
        }
    }
    
    return false;
}

/**
 * Rate limiting helper
 */
function isRateLimited($action, $limit = 5, $timeWindow = 900) { // 15 minutes default
    if (!isset($_SESSION['rate_limits'])) {
        $_SESSION['rate_limits'] = [];
    }
    
    if (!isset($_SESSION['rate_limits'][$action])) {
        $_SESSION['rate_limits'][$action] = [];
    }
    
    $now = time();
    
    // Clean old entries
    $_SESSION['rate_limits'][$action] = array_filter(
        $_SESSION['rate_limits'][$action],
        function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        }
    );
    
    // Check if limit exceeded
    if (count($_SESSION['rate_limits'][$action]) >= $limit) {
        return true;
    }
    
    // Record this attempt
    $_SESSION['rate_limits'][$action][] = $now;
    
    return false;
}

/**
 * Generate secure CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if IP is blocked
 */
function isIPBlocked($ip = null) {
    if ($ip === null) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    }

    // Check for common malicious IPs or patterns
    $blocked_patterns = [
        '127.0.0.1', // localhost (if not expected)
        '0.0.0.0',
        // Add more patterns as needed
    ];

    foreach ($blocked_patterns as $pattern) {
        if (strpos($ip, $pattern) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * Clean old security logs
 */
function cleanOldSecurityLogs($days = 30) {
    try {
        if (defined('DB_FILE') && file_exists(DB_FILE)) {
            $pdo = new PDO('sqlite:' . __DIR__ . '/../' . DB_FILE);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if security_logs table exists
            $result = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='security_logs'");
            if ($result->rowCount() == 0) {
                return 0; // Table doesn't exist
            }

            $stmt = $pdo->prepare("DELETE FROM security_logs WHERE timestamp < datetime('now', '-{$days} days')");
            $stmt->execute();

            return $stmt->rowCount();
        }
    } catch (Exception $e) {
        error_log("Error cleaning security logs: " . $e->getMessage());
    }

    return 0;
}

/**
 * Get security statistics
 */
function getSecurityStats($days = 7) {
    try {
        if (defined('DB_FILE') && file_exists(DB_FILE)) {
            $pdo = new PDO('sqlite:' . __DIR__ . '/../' . DB_FILE);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if security_logs table exists
            $result = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='security_logs'");
            if ($result->rowCount() == 0) {
                return null; // Table doesn't exist
            }

            $stats = [];

            // Total events in last X days
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM security_logs WHERE timestamp >= datetime('now', '-{$days} days')");
            $stmt->execute();
            $stats['total_events'] = $stmt->fetchColumn();

            // Events by type
            $stmt = $pdo->prepare("SELECT event_type, COUNT(*) as count FROM security_logs WHERE timestamp >= datetime('now', '-{$days} days') GROUP BY event_type ORDER BY count DESC");
            $stmt->execute();
            $stats['events_by_type'] = $stmt->fetchAll();

            // Top IPs
            $stmt = $pdo->prepare("SELECT ip_address, COUNT(*) as count FROM security_logs WHERE timestamp >= datetime('now', '-{$days} days') GROUP BY ip_address ORDER BY count DESC LIMIT 10");
            $stmt->execute();
            $stats['top_ips'] = $stmt->fetchAll();

            return $stats;
        }
    } catch (Exception $e) {
        error_log("Error getting security stats: " . $e->getMessage());
    }

    return null;
}
