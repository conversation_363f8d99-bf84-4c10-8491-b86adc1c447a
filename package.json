{"name": "smart-menu-system", "version": "2.0.0", "description": "نظام القائمة الذكية - Smart Menu System for Restaurants and Cafes", "keywords": ["menu", "restaurant", "cafe", "arabic", "responsive", "php", "sqlite"], "homepage": "https://github.com/smart-menu/system", "bugs": {"url": "https://github.com/smart-menu/system/issues"}, "license": "MIT", "author": {"name": "Smart Menu Team", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/smart-menu/system.git"}, "scripts": {"check": "php system_check.php", "install": "php install.php", "build": "echo 'No build process required for PHP project'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "deploy": "echo 'Please follow DEPLOYMENT.md guide'"}, "engines": {"php": ">=7.4.0"}, "devDependencies": {}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"]}