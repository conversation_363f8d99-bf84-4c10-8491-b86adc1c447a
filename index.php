<?php
// Redirect to installer if config is missing
if (!file_exists('config.php')) {
    header('Location: install.php');
    exit;
}

require_once 'config.php';
require_once 'includes/security.php';

// Initialize secure session (for language preference)
initSecureSession();

// Check for suspicious activity
if (detectSuspiciousActivity()) {
    logSecurityEvent('Suspicious Activity Blocked', 'Frontend access attempt');
    http_response_code(403);
    die('Access denied for security reasons.');
}

// Database connection
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/' . DB_FILE);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// Fetch settings
$stmt = $pdo->query("SELECT * FROM settings");
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Fetch categories
$stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC");
$categories = $stmt->fetchAll();

// Fetch pages
$stmt = $pdo->query("SELECT p.* FROM pages p JOIN categories c ON p.category_id = c.id WHERE p.is_active = 1 AND c.is_active = 1 ORDER BY c.sort_order ASC, p.sort_order ASC");
$pages = $stmt->fetchAll();

// Prepare data for JavaScript
$menu_data = [
    'categories' => [],
    'pages' => []
];

foreach ($categories as $category) {
    $menu_data['categories'][] = [
        'id' => (int)$category['id'],
        'name_ar' => $category['name_ar'],
        'name_en' => $category['name_en']
    ];
}

foreach ($pages as $page) {
    $menu_data['pages'][] = [
        'id' => (int)$page['id'],
        'category' => (int)$page['category_id'],
        'image_ar' => SITE_URL . '/uploads/' . $page['image_ar'],
        'image_en' => $page['image_en'] ? SITE_URL . '/uploads/' . $page['image_en'] : SITE_URL . '/uploads/' . $page['image_ar'] // Fallback to AR image
    ];
}

$active_langs = explode(',', $settings['lang_active']);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo htmlspecialchars($settings['site_title']); ?></title>
    <style>
        :root {
            --primary: <?php echo htmlspecialchars($settings['primary_color']); ?>;
            --secondary: <?php echo htmlspecialchars($settings['secondary_color']); ?>;
            --button-bg: <?php echo htmlspecialchars($settings['button_bg_color'] ?? '#FFFFFF'); ?>;
            --controls-height: 70px;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; -webkit-tap-highlight-color: transparent; }
        html, body { height: 100%; overflow: hidden; font-family: 'Tajawal', sans-serif; background-color: #f5f5f5; }
        
        .page-wrapper {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .menu-viewer {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .menu-page {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .page-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s;
        }

        .controls-container {
            height: var(--controls-height);
            flex-shrink: 0;
            background: var(--secondary);
            padding: 10px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .category-menu {
            display: flex;
            overflow-x: auto;
            gap: 8px;
            scrollbar-width: none;
        }
        .category-menu::-webkit-scrollbar { display: none; }

        .category-btn {
            padding: 8px 15px;
            background: var(--button-bg);
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        .category-btn.active { background: var(--primary); color: white; border-color: var(--primary); }

        /* Language Dropdown */
        .language-dropdown { position: absolute; top: 15px; z-index: 200; }
        html[dir="rtl"] .language-dropdown { left: 15px; }
        html[dir="ltr"] .language-dropdown { right: 15px; }
        .language-dropdown-toggle { background: rgba(255,255,255,0.8); border: 1px solid #ccc; border-radius: 20px; padding: 5px 15px; cursor: pointer; font-size: 14px; backdrop-filter: blur(5px); }
        .language-dropdown-menu { display: none; position: absolute; top: 100%; background: white; border-radius: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); list-style: none; padding: 5px 0; margin-top: 5px; min-width: 100px; }
        html[dir="rtl"] .language-dropdown-menu { left: 0; }
        html[dir="ltr"] .language-dropdown-menu { right: 0; }
        .language-dropdown-menu a { display: block; padding: 8px 15px; color: #333; text-decoration: none; }
        .language-dropdown-menu a:hover { background-color: #f5f5f5; }
        .language-dropdown-menu a.active { font-weight: bold; color: var(--primary); }

        /* Preloader */
        .preloader { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #fff; z-index: 9999; display: flex; flex-direction: column; justify-content: center; align-items: center; transition: opacity 0.5s ease; }
        .preloader.hidden { opacity: 0; pointer-events: none; }
        .spinner { width: 50px; height: 50px; border: 5px solid #f3f3f3; border-top: 5px solid var(--primary); border-radius: 50%; animation: spin 1s linear infinite; }
        .preloader-text { margin-top: 20px; font-size: 1rem; color: #666; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* Desktop view */
        /* Constrain aspect ratio on wide screens / landscape */
        @media (min-width: 700px) and (orientation: landscape) {
            body {
                justify-content: center;
                align-items: center;
            }
            .page-wrapper {
                width: 450px; /* Fixed width for landscape */
                height: 80vh; /* Fixed height */
                border: 1px solid #ddd;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body>

<div class="preloader" id="preloader">
    <div class="spinner"></div>
    <div class="preloader-text" id="preloaderText">جاري تحميل القائمة...</div>
</div>

<div class="page-wrapper">
    <?php if (count($active_langs) > 1): ?>
    <div class="language-dropdown" id="languageDropdown">
        <button class="language-dropdown-toggle" id="languageDropdownToggle">
            <?php echo $active_langs[0] === 'ar' ? 'العربية' : 'English'; ?>
        </button>
        <div class="language-dropdown-menu" id="languageDropdownMenu">
            <?php if (in_array('ar', $active_langs)): ?><a href="#" class="lang-link" data-lang="ar">العربية</a><?php endif; ?>
            <?php if (in_array('en', $active_langs)): ?><a href="#" class="lang-link" data-lang="en">English</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="menu-viewer" id="menuViewer"></div>
    
    <div class="controls-container">
        <div class="category-menu" id="categoryMenu"></div>
    </div>
</div>

<script>
    const menuData = <?php echo json_encode($menu_data, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK); ?>;
    const translations = {
        ar: { errorTitle: "خطأ بالتحميل", errorText: "تعذر تحميل الصفحة المطلوبة.", retry: "إعادة المحاولة" },
        en: { errorTitle: "Loading Error", errorText: "Failed to load the requested page.", retry: "Retry" }
    };

    let currentLang = localStorage.getItem('menuLang') || '<?php echo $active_langs[0]; ?>';
    let currentPageIndex = 0;
    let isDragging = false;
    let startX = 0;
    let currentTranslate = 0;

    const menuViewer = document.getElementById('menuViewer');
    const categoryMenu = document.getElementById('categoryMenu');

    document.addEventListener('DOMContentLoaded', init);

    async function init() {
        const preloader = document.getElementById('preloader');
        if (!menuData.pages || menuData.pages.length === 0) {
            menuViewer.innerHTML = '<p style="text-align:center; padding: 20px;">لا توجد صفحات لعرضها حالياً.</p>';
            preloader.classList.add('hidden');
            return;
        }

        createPages();
        createCategories();
        setupEventListeners();
        setupLanguageDropdown();
        
        await preloadImages();
        
        updateView();
        document.documentElement.lang = currentLang;
        document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
    }

    function createPages() {
        menuViewer.innerHTML = '';
        menuData.pages.forEach((page, index) => {
            const pageEl = document.createElement('div');
            pageEl.className = 'menu-page';
            pageEl.dataset.index = index;
            
            const imgEl = document.createElement('img');
            imgEl.className = 'page-image';
            imgEl.style.opacity = '0';
            imgEl.alt = `Page ${index + 1}`;
            
            pageEl.appendChild(imgEl);
            menuViewer.appendChild(pageEl);
        });
    }

    function createCategories() {
        categoryMenu.innerHTML = '';
        menuData.categories.forEach(category => {
            const btn = document.createElement('div');
            btn.className = 'category-btn';
            btn.dataset.category = category.id;
            btn.addEventListener('click', () => {
                const firstPage = menuData.pages.findIndex(p => p.category === category.id);
                if (firstPage !== -1) goToPage(firstPage);
            });
            categoryMenu.appendChild(btn);
        });
        updateCategoryNames();
    }
    
    function updateCategoryNames() {
        document.querySelectorAll('.category-btn').forEach(btn => {
            const catId = parseInt(btn.dataset.category);
            const category = menuData.categories.find(c => c.id === catId);
            if(category) {
                btn.textContent = currentLang === 'ar' ? category.name_ar : category.name_en;
            }
        });
    }

    function setupEventListeners() {
        menuViewer.addEventListener('touchstart', dragStart, { passive: true });
        menuViewer.addEventListener('touchmove', drag, { passive: false });
        menuViewer.addEventListener('touchend', dragEnd);
        menuViewer.addEventListener('mousedown', dragStart);
        menuViewer.addEventListener('mousemove', drag);
        menuViewer.addEventListener('mouseup', dragEnd);
        menuViewer.addEventListener('mouseleave', dragEnd);
    }

    function setupLanguageDropdown() {
        const dropdown = document.getElementById('languageDropdown');
        if (!dropdown) return;

        const toggle = document.getElementById('languageDropdownToggle');
        const menu = document.getElementById('languageDropdownMenu');
        const links = menu.querySelectorAll('.lang-link');

        function updateToggleText() {
            const activeLink = menu.querySelector(`.lang-link[data-lang="${currentLang}"]`);
            if (activeLink) {
                toggle.textContent = activeLink.textContent;
                links.forEach(l => l.classList.remove('active'));
                activeLink.classList.add('active');
            }
        }

        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
        });

        document.addEventListener('click', () => {
            menu.style.display = 'none';
        });

        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                currentLang = this.dataset.lang;
                localStorage.setItem('menuLang', currentLang);
                
                document.documentElement.lang = currentLang;
                document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';
                
                updateToggleText();
                updateCategoryNames();
                updateView(false);
            });
        });
        
        updateToggleText();
    }

    function goToPage(index) {
        if (index < 0 || index >= menuData.pages.length) return;
        currentPageIndex = index;
        updateView();
    }

    function updateView(withTransition = true) {
        const pages = document.querySelectorAll('.menu-page');
        pages.forEach((page, index) => {
            page.style.transition = withTransition ? 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)' : 'none';
            let offset = index - currentPageIndex;
            if (currentLang === 'ar') {
                offset = -offset;
            }
            page.style.transform = `translateX(${offset * 100}%)`;
        });
        
        loadCurrentImage();
        updateActiveCategory();
    }
    
    function preloadImages() {
        const preloader = document.getElementById('preloader');
        const preloaderText = document.getElementById('preloaderText');
        
        const imagesToLoad = [];
        menuData.pages.forEach(page => {
            if (page.image_ar) imagesToLoad.push(page.image_ar);
            if (page.image_en && page.image_en !== page.image_ar) imagesToLoad.push(page.image_en);
        });

        if (imagesToLoad.length === 0) {
            preloader.classList.add('hidden');
            return Promise.resolve();
        }

        const promises = imagesToLoad.map((src, index) => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    preloaderText.textContent = `جاري تحميل الصور... (${index + 1}/${imagesToLoad.length})`;
                    resolve();
                };
                img.onerror = () => {
                    preloaderText.textContent = `جاري تحميل الصور... (${index + 1}/${imagesToLoad.length})`;
                    resolve(); // Resolve even on error to not block the app
                };
                img.src = src;
            });
        });

        return Promise.all(promises).then(() => {
            setTimeout(() => {
                preloader.classList.add('hidden');
            }, 300);
        });
    }

    function loadCurrentImage() {
        const pageElement = document.querySelector(`.menu-page[data-index="${currentPageIndex}"]`);
        if (!pageElement) return;
        
        const imgEl = pageElement.querySelector('.page-image');
        const pageData = menuData.pages[currentPageIndex];
        const imageUrl = currentLang === 'en' && pageData.image_en ? pageData.image_en : pageData.image_ar;

        if (imgEl.src === imageUrl) return;

        imgEl.style.opacity = '0';
        
        const img = new Image();
        img.src = imageUrl;
        img.onload = () => {
            imgEl.src = imageUrl;
            imgEl.style.opacity = '1';
        };
        img.onerror = () => {
            imgEl.alt = translations[currentLang].errorText;
        };
    }

    function updateActiveCategory() {
        const currentCategory = menuData.pages[currentPageIndex]?.category;
        const categoryBtns = document.querySelectorAll('.category-btn');
        let activeBtn = null;
        categoryBtns.forEach(btn => {
            const isActive = parseInt(btn.dataset.category) === currentCategory;
            btn.classList.toggle('active', isActive);
            if (isActive) activeBtn = btn;
        });
        if (activeBtn) {
            activeBtn.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }

    // Drag functions
    function dragStart(e) {
        isDragging = true;
        startX = getPositionX(e);
        menuViewer.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();
            const currentX = getPositionX(e);
            currentTranslate = currentX - startX;
        }
    }

    function dragEnd() {
        if (!isDragging) return;
        isDragging = false;
        menuViewer.style.cursor = 'grab';

        const threshold = menuViewer.clientWidth / 5;
        const swipeLeft = currentTranslate < -threshold;
        const swipeRight = currentTranslate > threshold;
        const isRTL = currentLang === 'ar';

        if ((isRTL && swipeRight) || (!isRTL && swipeLeft)) {
            if (currentPageIndex < menuData.pages.length - 1) {
                currentPageIndex++;
            }
        } else if ((isRTL && swipeLeft) || (!isRTL && swipeRight)) {
            if (currentPageIndex > 0) {
                currentPageIndex--;
            }
        }
        
        currentTranslate = 0;
        updateView();
    }

    function getPositionX(e) {
        return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }
</script>
</body>
</html>