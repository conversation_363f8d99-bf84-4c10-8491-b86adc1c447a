# 🚀 دليل الإعداد السريع - Quick Setup Guide

## للمستخدمين الجدد

### 1. فحص متطلبات النظام
```
افتح: system_check.php
تأكد من أن جميع المتطلبات متوفرة
```

### 2. التثبيت
```
افتح: install.php
اتبع التعليمات على الشاشة
```

### 3. الدخول للوحة التحكم
```
الرابط: admin.php
المستخدم: admin
كلمة المرور: admin
```

### 4. تغيير كلمة المرور
```
اذهب إلى: الإعدادات > تغيير كلمة المرور
```

---

## للمطورين

### هيكل المشروع
```
smart-menu-system/
├── 📄 index.php              # الواجهة الأمامية
├── 🔐 admin.php             # لوحة التحكم
├── ⚙️ install.php           # مثبت النظام
├── 🔍 system_check.php      # فحص المتطلبات
├── 📋 config.php            # الإعدادات (يُنشأ تلقائياً)
├── 🛡️ .htaccess            # حماية الخادم
├── 📚 README.md             # الوثائق الرئيسية
├── 📖 دليل_المستخدم.html    # دليل شامل
├── 📝 CHANGELOG.md          # سجل التغييرات
├── ⚖️ LICENSE               # الترخيص
├── 🗂️ admin/               # ملفات الإدارة
│   ├── dashboard.php        # الرئيسية
│   ├── categories.php       # الأقسام
│   ├── pages.php           # الصفحات
│   ├── settings.php        # الإعدادات
│   ├── security.php        # الأمان
│   ├── header.php          # الرأس
│   └── footer.php          # التذييل
├── 🔧 includes/            # المكتبات
│   ├── security.php        # وظائف الأمان
│   └── image_optimizer.php # تحسين الصور
├── 🎨 assets/              # الموارد
│   ├── css/               # التنسيقات
│   └── js/                # السكريبتات
├── 📁 uploads/             # الصور المرفوعة
├── 📊 logs/                # السجلات
└── 🗄️ database.db          # قاعدة البيانات
```

### قاعدة البيانات
```sql
-- الجداول الرئيسية
settings        # إعدادات النظام
categories      # أقسام القائمة
pages          # صفحات القائمة
security_logs  # سجلات الأمان
```

### الأمان المطبق
- ✅ CSRF Protection
- ✅ Rate Limiting
- ✅ Input Sanitization
- ✅ SQL Injection Prevention
- ✅ XSS Protection
- ✅ File Upload Security
- ✅ Session Security
- ✅ Security Logging

### API الداخلي
```php
// وظائف الأمان
initSecureSession()
sanitizeInput($input, $type)
validateImageUpload($file)
logSecurityEvent($event, $details)
isRateLimited($action, $limit, $window)

// وظائف تحسين الصور
ImageOptimizer->processImage($file, $filename)
ImageOptimizer->getImageUrl($filename, $size)
```

---

## التخصيص

### تغيير الألوان
```css
:root {
    --primary: #8B4513;      /* اللون الأساسي */
    --secondary: #FFFFFF;    /* اللون الثانوي */
    --button-bg: #FFFFFF;    /* خلفية الأزرار */
}
```

### إضافة لغة جديدة
```php
// في index.php
$translations = [
    'ar' => [...],
    'en' => [...],
    'fr' => [...]  // لغة جديدة
];
```

### تخصيص التصميم
```php
// في admin/header.php
// تعديل CSS المخصص
```

---

## النشر

### متطلبات الخادم
- PHP 7.4+
- SQLite Support
- GD Extension
- mod_rewrite (اختياري)
- SSL Certificate (مُوصى به)

### خطوات النشر
1. رفع الملفات للخادم
2. تعيين أذونات المجلدات (755)
3. تشغيل system_check.php
4. تشغيل install.php
5. تغيير كلمة المرور
6. حذف ملفات التثبيت (اختياري)

### الأمان في الإنتاج
```apache
# في .htaccess
# تم تطبيق جميع إعدادات الأمان تلقائياً
```

---

## التطوير

### إضافة ميزة جديدة
1. إنشاء ملف في admin/
2. إضافة الرابط في admin.php
3. تحديث admin/header.php
4. تطبيق معايير الأمان

### اختبار النظام
```bash
# فحص الأمان
php system_check.php

# اختبار قاعدة البيانات
sqlite3 database.db ".tables"

# مراجعة السجلات
tail -f logs/security.log
```

---

## الدعم

### الأخطاء الشائعة
- **خطأ أذونات**: `chmod 755 . && chmod 644 *.php`
- **مشكلة SQLite**: تحقق من دعم PHP للـ SQLite
- **رفع الصور**: تحقق من `upload_max_filesize`

### التحديث
1. نسخ احتياطي للبيانات
2. رفع الملفات الجديدة
3. تشغيل سكريبت التحديث (إن وُجد)
4. مراجعة الإعدادات

---

## المساهمة

### إرشادات الكود
- استخدم التعليقات العربية
- اتبع معايير PSR-12
- طبق جميع معايير الأمان
- اختبر الكود قبل الإرسال

### الإبلاغ عن الأخطاء
- وصف واضح للمشكلة
- خطوات إعادة الإنتاج
- معلومات البيئة (PHP، OS، إلخ)
- لقطات شاشة (إن أمكن)

---

**نظام القائمة الذكية - تم التطوير بـ ❤️ للمجتمع العربي**
