# 🚀 دليل النشر - Deployment Guide

## قبل النشر

### ✅ قائمة التحقق
- [ ] تم اختبار النظام محلياً
- [ ] تم فحص جميع الملفات المطلوبة
- [ ] تم مراجعة إعدادات الأمان
- [ ] تم إنشاء نسخة احتياطية (إن وُجدت)

### 📋 الملفات المطلوبة
```
✅ index.php
✅ admin.php
✅ install.php
✅ system_check.php
✅ .htaccess
✅ admin/ (مجلد كامل)
✅ includes/ (مجلد كامل)
✅ assets/ (مجلد كامل)
✅ uploads/ (مجلد فارغ مع .htaccess)
✅ logs/ (مجلد فارغ مع .htaccess)
```

### ❌ الملفات التي لا تُرفع
```
❌ config.php (يُنشأ تلقائياً)
❌ database.db (يُنشأ تلقائياً)
❌ composer.json (اختياري)
❌ .git/ (إن وُجد)
❌ README.md (اختياري)
❌ ملفات التطوير والاختبار
```

---

## خطوات النشر

### 1. إعداد الخادم
```bash
# تأكد من توفر PHP 7.4+
php -v

# تأكد من دعم SQLite
php -m | grep sqlite3

# تأكد من دعم GD
php -m | grep gd
```

### 2. رفع الملفات
```bash
# عبر FTP/SFTP
# ارفع جميع الملفات إلى مجلد الموقع
# تأكد من الحفاظ على هيكل المجلدات
```

### 3. تعيين الأذونات
```bash
# أذونات المجلد الرئيسي
chmod 755 /path/to/website

# أذونات الملفات
find . -type f -name "*.php" -exec chmod 644 {} \;

# أذونات المجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 755 admin/
chmod 755 includes/
chmod 755 assets/

# أذونات خاصة
chmod 644 .htaccess
chmod 644 uploads/.htaccess
chmod 644 logs/.htaccess
```

### 4. فحص النظام
```
1. افتح: https://yoursite.com/system_check.php
2. تأكد من أن جميع المتطلبات متوفرة
3. حل أي مشاكل قبل المتابعة
```

### 5. تشغيل المثبت
```
1. افتح: https://yoursite.com/install.php
2. اتبع التعليمات على الشاشة
3. انتظر حتى اكتمال التثبيت
```

### 6. الإعداد الأولي
```
1. اذهب إلى: https://yoursite.com/admin.php
2. سجل دخول بـ: admin / admin
3. غيّر كلمة المرور فوراً
4. اضبط إعدادات الموقع
```

---

## إعدادات الخادم

### Apache
```apache
# في .htaccess (مُطبق تلقائياً)
RewriteEngine On
Options -Indexes

# Security headers
Header always set X-Frame-Options SAMEORIGIN
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
```

### Nginx
```nginx
server {
    listen 80;
    server_name yoursite.com;
    root /path/to/website;
    index index.php;

    # Security headers
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # Deny access to sensitive files
    location ~ /\.(htaccess|htpasswd|ini|log|sh|inc|bak) {
        deny all;
    }

    # Deny access to sensitive directories
    location ~ ^/(logs|includes)/ {
        deny all;
    }

    # Prevent script execution in uploads
    location ~* ^/uploads/.*\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$ {
        deny all;
    }
}
```

### PHP Configuration
```ini
# في php.ini أو .htaccess
upload_max_filesize = 8M
post_max_size = 8M
max_execution_time = 30
memory_limit = 128M
file_uploads = On

# Security settings
expose_php = Off
display_errors = Off
log_errors = On
```

---

## SSL/HTTPS

### إعداد SSL
```bash
# باستخدام Let's Encrypt
certbot --apache -d yoursite.com

# أو باستخدام Cloudflare
# فعّل SSL من لوحة تحكم Cloudflare
```

### إجبار HTTPS
```apache
# في .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

---

## النسخ الاحتياطي

### النسخ الاحتياطي التلقائي
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"

# نسخ قاعدة البيانات
cp database.db "$BACKUP_DIR/database_$DATE.db"

# نسخ الصور
tar -czf "$BACKUP_DIR/uploads_$DATE.tar.gz" uploads/

# نسخ الإعدادات
cp config.php "$BACKUP_DIR/config_$DATE.php"

# حذف النسخ القديمة (أكثر من 30 يوم)
find "$BACKUP_DIR" -name "*.db" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.php" -mtime +30 -delete
```

### جدولة النسخ الاحتياطي
```bash
# في crontab
# نسخة احتياطية يومية في الساعة 2:00 صباحاً
0 2 * * * /path/to/backup.sh
```

---

## المراقبة والصيانة

### مراقبة السجلات
```bash
# مراقبة سجلات الأمان
tail -f logs/security.log

# مراقبة سجلات الخادم
tail -f /var/log/apache2/error.log
```

### الصيانة الدورية
```bash
# تنظيف السجلات القديمة (شهرياً)
# من لوحة التحكم > سجلات الأمان > تنظيف السجلات

# تحديث النظام (عند توفر تحديثات)
# تحميل الإصدار الجديد
# عمل نسخة احتياطية
# رفع الملفات الجديدة
# تشغيل سكريبت التحديث (إن وُجد)
```

---

## استكشاف الأخطاء

### مشاكل شائعة

**1. خطأ 500 - Internal Server Error**
```bash
# تحقق من سجلات الخادم
tail -f /var/log/apache2/error.log

# تحقق من أذونات الملفات
ls -la

# تحقق من صحة .htaccess
```

**2. مشكلة في رفع الصور**
```bash
# تحقق من أذونات مجلد uploads
chmod 755 uploads/

# تحقق من إعدادات PHP
php -i | grep upload_max_filesize
```

**3. مشكلة في قاعدة البيانات**
```bash
# تحقق من أذونات الكتابة
ls -la database.db

# تحقق من دعم SQLite
php -m | grep sqlite3
```

---

## الأمان في الإنتاج

### قائمة تحقق الأمان
- [ ] تم تغيير كلمة مرور المدير
- [ ] تم تفعيل HTTPS
- [ ] تم تطبيق جميع إعدادات .htaccess
- [ ] تم حماية المجلدات الحساسة
- [ ] تم إخفاء معلومات PHP
- [ ] تم تفعيل مراقبة السجلات

### مراقبة الأمان
```bash
# مراجعة سجلات الأمان أسبوعياً
grep "Failed Login" logs/security.log

# مراقبة محاولات الوصول المشبوهة
grep "Suspicious Activity" logs/security.log
```

---

## الدعم

### في حالة المشاكل
1. راجع سجلات الأخطاء
2. تحقق من متطلبات النظام
3. راجع الوثائق
4. تواصل مع الدعم التقني

### معلومات مفيدة للدعم
- إصدار PHP
- نوع الخادم (Apache/Nginx)
- رسالة الخطأ الكاملة
- خطوات إعادة الإنتاج

---

**نجح النشر؟ 🎉 مبروك! موقعك جاهز الآن.**
