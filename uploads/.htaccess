# Prevent script execution in uploads directory
<Files "*.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.phtml">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php3">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php4">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php5">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.pl">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.py">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.jsp">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.asp">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.sh">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.cgi">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Allow only image files
<FilesMatch "\.(jpg|jpeg|png|gif|webp|svg)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Disable directory browsing
Options -Indexes
