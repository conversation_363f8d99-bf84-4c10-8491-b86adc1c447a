{"name": "smart-menu/system", "description": "نظام القائمة الذكية - Smart Menu System for Restaurants and Cafes", "type": "project", "keywords": ["menu", "restaurant", "cafe", "arabic", "php", "sqlite", "responsive", "قائمة", "مطعم", "<PERSON><PERSON><PERSON><PERSON>"], "homepage": "https://github.com/smart-menu/system", "license": "MIT", "authors": [{"name": "Smart Menu Team", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/smart-menu/system/issues", "source": "https://github.com/smart-menu/system", "docs": "https://github.com/smart-menu/system/wiki"}, "require": {"php": ">=7.4.0", "ext-sqlite3": "*", "ext-gd": "*", "ext-json": "*", "ext-mbstring": "*"}, "suggest": {"ext-curl": "Required for future integrations", "ext-openssl": "Required for HTTPS support"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "scripts": {"post-install-cmd": ["@php -r \"if (!file_exists('config.php')) { echo 'Please run install.php to complete setup\\n'; }\""], "check-system": ["@php system_check.php"], "install-system": ["@php install.php"]}, "minimum-stability": "stable", "prefer-stable": true}