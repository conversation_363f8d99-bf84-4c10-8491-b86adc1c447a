<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// If config exists, redirect to index
if (file_exists('config.php')) {
    header('Location: index.php');
    exit;
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$errors = [];
$db_file = 'database.db';
$db_path = __DIR__ . '/' . $db_file;

function check_php_version() {
    return version_compare(PHP_VERSION, '7.4', '>=');
}

function check_pdo_sqlite() {
    return extension_loaded('pdo_sqlite');
}

function check_write_permission() {
    return is_writable(__DIR__);
}

// Step 2: Create database and config
if ($step === 2) {
    if (!check_php_version() || !check_pdo_sqlite() || !check_write_permission()) {
        header('Location: install.php?step=1');
        exit;
    }

    try {
        // Create and connect to SQLite database
        $pdo = new PDO('sqlite:' . $db_path);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // SQL to create tables for SQLite
        $sql = "
        CREATE TABLE `settings` (
          `setting_key` TEXT NOT NULL PRIMARY KEY,
          `setting_value` TEXT
        );

        INSERT INTO `settings` (`setting_key`, `setting_value`) VALUES
        ('admin_user', 'admin'),
        ('admin_pass', '" . password_hash('admin', PASSWORD_DEFAULT) . "'),
        ('primary_color', '#8B4513'),
        ('secondary_color', '#FFFFFF'),
        ('button_bg_color', '#FFFFFF'),
        ('lang_active', 'ar,en'),
        ('site_title', 'القائمة الذكية');

        CREATE TABLE `categories` (
          `id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
          `name_ar` TEXT NOT NULL,
          `name_en` TEXT,
          `sort_order` INTEGER DEFAULT 0,
          `is_active` INTEGER DEFAULT 1
        );

        INSERT INTO `categories` (`id`, `name_ar`, `name_en`, `sort_order`, `is_active`) VALUES
        (1, 'الغلاف', 'Cover', 0, 1),
        (2, 'العصائر', 'Juices', 1, 1);

        CREATE TABLE `pages` (
          `id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
          `category_id` INTEGER NOT NULL,
          `image_ar` TEXT,
          `image_en` TEXT,
          `sort_order` INTEGER DEFAULT 0,
          `is_active` INTEGER DEFAULT 1,
          FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
        );

        CREATE TABLE `security_logs` (
          `id` INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
          `timestamp` DATETIME DEFAULT CURRENT_TIMESTAMP,
          `ip_address` TEXT,
          `user_agent` TEXT,
          `event_type` TEXT NOT NULL,
          `details` TEXT,
          `severity` TEXT DEFAULT 'INFO'
        );

        CREATE INDEX idx_security_logs_timestamp ON security_logs(timestamp);
        CREATE INDEX idx_security_logs_event_type ON security_logs(event_type);
        CREATE INDEX idx_security_logs_ip ON security_logs(ip_address);
        ";

        $pdo->exec($sql);

        // Insert initial security log
        $stmt = $pdo->prepare("INSERT INTO security_logs (event_type, details, severity, ip_address) VALUES (?, ?, ?, ?)");
        $stmt->execute(['System Installation', 'Menu system installed successfully', 'INFO', $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        
        // Create uploads directory
        if (!is_dir('uploads')) {
            mkdir('uploads', 0755, true);
        }

        // Create config file
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $script_dir = str_replace(basename($_SERVER['SCRIPT_NAME']), '', $_SERVER['SCRIPT_NAME']);
        $site_url = rtrim("$protocol://$host$script_dir", '/');

        $config_content = "<?php\n\n";
        $config_content .= "define('DB_FILE', '" . addslashes($db_file) . "');\n";
        $config_content .= "define('SITE_URL', '" . $site_url . "');\n";

        if (!file_put_contents('config.php', $config_content)) {
             throw new Exception('لا يمكن كتابة ملف config.php. يرجى التحقق من أذونات المجلد.');
        }

    } catch (Exception $e) {
        $errors[] = 'فشل التثبيت: ' . $e->getMessage();
        // Clean up on failure
        if (file_exists($db_path)) unlink($db_path);
        if (file_exists('config.php')) unlink('config.php');
    }
}

// Step 3: Set admin user
if ($step === 3 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $admin_user = $_POST['admin_user'];
    $admin_pass = $_POST['admin_pass'];

    if (empty($admin_user) || empty($admin_pass)) {
        $errors[] = 'اسم المستخدم وكلمة المرور مطلوبان.';
    } else {
        try {
            $pdo = new PDO('sqlite:' . $db_path);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $hashed_password = password_hash($admin_pass, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = 'admin_user'");
            $stmt->execute([$admin_user]);
            
            $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = 'admin_pass'");
            $stmt->execute([$hashed_password]);
            
            // Self-destruct installer
            unlink(__FILE__);
            
            session_destroy();
            header('Location: admin.php');
            exit;
        } catch (Exception $e) {
            $errors[] = 'فشل تحديث حساب المدير: ' . $e->getMessage();
        }
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام القائمة الذكية</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; background-color: #f8f9fa; color: #333; line-height: 1.6; margin: 0; padding: 20px; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
        .container { max-width: 600px; width: 100%; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        h1, h2 { color: #8B4513; text-align: center; }
        .step-box { border: 1px solid #ddd; padding: 20px; margin-top: 20px; border-radius: 5px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
        .btn { display: inline-block; background-color: #8B4513; color: #fff; padding: 12px 25px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 16px; text-align: center; width: 100%; }
        .btn:hover { background-color: #5D4037; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        ul { padding-right: 20px; list-style: none; }
        li { margin-bottom: 10px; position: relative; padding-right: 25px; }
        li::before { content: ''; position: absolute; right: 0; top: 5px; width: 10px; height: 10px; border-radius: 50%; }
        li.success::before { background-color: #28a745; }
        li.error::before { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>تثبيت نظام القائمة الذكية (SQLite)</h1>

        <div style="text-align: center; margin-bottom: 20px;">
            <a href="system_check.php" class="btn" style="width: auto; background: #17a2b8; margin-bottom: 20px;">🔍 فحص متطلبات النظام</a>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <?php foreach ($errors as $error): ?>
                    <p><?php echo $error; ?></p>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if ($step === 1): ?>
            <div class="step-box">
                <h2>الخطوة 1: التحقق من المتطلبات</h2>
                <ul>
                    <li class="<?php echo check_php_version() ? 'success' : 'error'; ?>">إصدار PHP >= 7.4</li>
                    <li class="<?php echo check_pdo_sqlite() ? 'success' : 'error'; ?>">إضافة PDO SQLite</li>
                    <li class="<?php echo check_write_permission() ? 'success' : 'error'; ?>">أذونات الكتابة في المجلد الحالي</li>
                </ul>
                <?php if (check_php_version() && check_pdo_sqlite() && check_write_permission()): ?>
                    <a href="?step=2" class="btn">بدء التثبيت</a>
                <?php else: ?>
                    <p class="alert alert-danger">يرجى التأكد من أن بيئة الاستضافة تلبي المتطلبات أعلاه.</p>
                <?php endif; ?>
            </div>
        <?php elseif ($step === 2 && empty($errors)): ?>
             <div class="step-box">
                <h2>الخطوة 2: إنشاء حساب المدير</h2>
                <div class="alert alert-success">تم إنشاء قاعدة البيانات وملف الإعدادات بنجاح.</div>
                 <form action="?step=3" method="post">
                    <div class="form-group">
                        <label for="admin_user">اسم مستخدم المدير</label>
                        <input type="text" id="admin_user" name="admin_user" value="admin" required>
                    </div>
                    <div class="form-group">
                        <label for="admin_pass">كلمة مرور المدير</label>
                        <input type="password" id="admin_pass" name="admin_pass" required>
                    </div>
                    <button type="submit" class="btn">إنهاء التثبيت</button>
                </form>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>